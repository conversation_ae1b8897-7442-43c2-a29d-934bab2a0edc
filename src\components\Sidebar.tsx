import type React from 'react';
import { X, Plus, Calendar, Clock, BookOpen } from 'lucide-react';
import type { StudySchedule } from '../types';
import { formatDateShort, sortSchedulesByDate } from '../utils/helpers';

interface SidebarProps {
  isOpen: boolean;
  schedules: StudySchedule[];
  onSelectSchedule: (schedule: StudySchedule) => void;
  onCreateSchedule: () => void;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  isOpen,
  schedules,
  onSelectSchedule,
  onCreateSchedule,
  onClose
}) => {
  const sortedSchedules = sortSchedulesByDate(schedules);

  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed left-0 top-16 h-[calc(100vh-4rem)] w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out z-50 ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0 lg:static lg:top-0 lg:h-full lg:shadow-none lg:border-r lg:border-gray-200 lg:dark:border-gray-700`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 lg:hidden">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Cronogramas
            </h2>
            <button
              onClick={onClose}
              className="p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Create button */}
          <div className="p-4">
            <button
              onClick={() => {
                onCreateSchedule();
                onClose();
              }}
              className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-800 transition-all duration-200"
            >
              <Plus className="h-4 w-4 mr-2" />
              Novo Cronograma
            </button>
          </div>

          {/* Schedules list */}
          <div className="flex-1 overflow-y-auto">
            <div className="px-4 pb-4">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                Seus Cronogramas
              </h3>
              
              {sortedSchedules.length === 0 ? (
                <div className="text-center py-8">
                  <BookOpen className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-3" />
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Nenhum cronograma criado ainda
                  </p>
                  <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                    Clique em "Novo Cronograma" para começar
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  {sortedSchedules.map((schedule) => (
                    <button
                      key={schedule.id}
                      onClick={() => {
                        onSelectSchedule(schedule);
                        onClose();
                      }}
                      className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-purple-300 dark:hover:border-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-200 group"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate group-hover:text-purple-700 dark:group-hover:text-purple-300">
                            {schedule.title}
                          </h4>
                          {schedule.description && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                              {schedule.description}
                            </p>
                          )}
                          <div className="flex items-center mt-2 space-x-3 text-xs text-gray-400 dark:text-gray-500">
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {schedule.weeks.length} semana{schedule.weeks.length !== 1 ? 's' : ''}
                            </div>
                            <div className="flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              {formatDateShort(new Date(schedule.createdAt))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
