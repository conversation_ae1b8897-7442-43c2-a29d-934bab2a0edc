import type React from 'react';
import { X, Plus, Calendar, Clock, BookOpen } from 'lucide-react';
import type { StudySchedule } from '../types';
import { formatDateShort, sortSchedulesByDate } from '../utils/helpers';

interface SidebarProps {
  isOpen: boolean;
  schedules: StudySchedule[];
  onSelectSchedule: (schedule: StudySchedule) => void;
  onCreateSchedule: () => void;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  isOpen,
  schedules,
  onSelectSchedule,
  onCreateSchedule,
  onClose
}) => {
  const sortedSchedules = sortSchedulesByDate(schedules);

  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed left-0 top-20 h-[calc(100vh-5rem)] w-72 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 shadow-2xl transform transition-all duration-300 ease-in-out z-50 backdrop-blur-sm border-r border-gray-700/50 ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0 lg:static lg:top-0 lg:h-full`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-700/50 lg:hidden">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                <BookOpen className="h-4 w-4 text-white" />
              </div>
              <h2 className="text-lg font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                Cronogramas
              </h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-xl text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-200 backdrop-blur-sm"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Create button */}
          <div className="p-6">
            <button
              onClick={() => {
                onCreateSchedule();
                onClose();
              }}
              className="w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 focus:ring-offset-gray-900 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <Plus className="h-4 w-4 mr-2" />
              Novo Cronograma
            </button>
          </div>

          {/* Schedules list */}
          <div className="flex-1 overflow-y-auto scrollbar-hide">
            <div className="px-6 pb-6">
              <h3 className="text-sm font-bold text-gray-300 uppercase tracking-wider mb-4 flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-purple-400" />
                Seus Cronogramas
              </h3>

              {sortedSchedules.length === 0 ? (
                <div className="text-center py-12">
                  <div className="relative mx-auto w-16 h-16 mb-4">
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full opacity-20 animate-pulse"></div>
                    <div className="relative w-full h-full bg-gray-700/50 rounded-full flex items-center justify-center backdrop-blur-sm">
                      <BookOpen className="h-8 w-8 text-gray-400" />
                    </div>
                  </div>
                  <p className="text-sm text-gray-400 font-medium">
                    Nenhum cronograma criado ainda
                  </p>
                  <p className="text-xs text-gray-500 mt-2">
                    Clique em "Novo Cronograma" para começar
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {sortedSchedules.map((schedule, index) => (
                    <button
                      key={schedule.id}
                      onClick={() => {
                        onSelectSchedule(schedule);
                        onClose();
                      }}
                      className="w-full text-left p-4 rounded-xl bg-gray-800/50 border border-gray-700/50 hover:border-purple-500/50 hover:bg-gradient-to-r hover:from-purple-900/20 hover:to-blue-900/20 transition-all duration-300 group backdrop-blur-sm transform hover:scale-[1.02]"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center mb-2">
                            <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full mr-3 group-hover:animate-pulse"></div>
                            <h4 className="text-sm font-bold text-white truncate group-hover:text-purple-300 transition-colors duration-200">
                              {schedule.title}
                            </h4>
                          </div>
                          {schedule.description && (
                            <p className="text-xs text-gray-400 mt-1 line-clamp-2 group-hover:text-gray-300 transition-colors duration-200">
                              {schedule.description}
                            </p>
                          )}
                          <div className="flex items-center mt-3 space-x-4 text-xs text-gray-500">
                            <div className="flex items-center bg-purple-900/30 px-2 py-1 rounded-full">
                              <Calendar className="h-3 w-3 mr-1 text-purple-400" />
                              <span className="font-medium">{schedule.weeks.length} semana{schedule.weeks.length !== 1 ? 's' : ''}</span>
                            </div>
                            <div className="flex items-center bg-blue-900/30 px-2 py-1 rounded-full">
                              <Clock className="h-3 w-3 mr-1 text-blue-400" />
                              <span className="font-medium">{formatDateShort(new Date(schedule.createdAt))}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
