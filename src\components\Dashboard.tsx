import React, { useState } from 'react';
import { Plus, Search, Calendar, Clock, BookOpen, Edit, Trash2, Eye, Copy, Download } from 'lucide-react';
import type { StudySchedule } from '../types';
import { formatDateShort, sortSchedulesByDate, searchSchedules } from '../utils/helpers';
import { exportAllSchedulesAsJSON } from '../utils/export';

interface DashboardProps {
  schedules: StudySchedule[];
  onCreateSchedule: () => void;
  onEditSchedule: (schedule: StudySchedule) => void;
  onViewSchedule: (schedule: StudySchedule) => void;
  onDeleteSchedule: (scheduleId: string) => void;
  onDuplicateSchedule?: (schedule: StudySchedule) => void;
}

const Dashboard: React.FC<DashboardProps> = ({
  schedules,
  onCreateSchedule,
  onEditSchedule,
  onViewSchedule,
  onDeleteSchedule,
  onDuplicateSchedule
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

  const filteredSchedules = searchSchedules(sortSchedulesByDate(schedules), searchQuery);

  const handleDelete = (scheduleId: string) => {
    if (deleteConfirm === scheduleId) {
      onDeleteSchedule(scheduleId);
      setDeleteConfirm(null);
    } else {
      setDeleteConfirm(scheduleId);
      // Auto-cancel confirmation after 3 seconds
      setTimeout(() => setDeleteConfirm(null), 3000);
    }
  };

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Seus Cronogramas de Estudo
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Organize e gerencie seus cronogramas semanais de forma eficiente
        </p>
      </div>

      {/* Search and Create */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Buscar cronogramas..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          />
        </div>
        <div className="flex items-center space-x-3">
          {schedules.length > 0 && (
            <button
              onClick={() => exportAllSchedulesAsJSON(schedules)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-900 transition-all duration-200"
            >
              <Download className="h-4 w-4 mr-2" />
              Exportar Todos
            </button>
          )}
          <button
            onClick={onCreateSchedule}
            className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-900 transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <Plus className="h-4 w-4 mr-2" />
            Novo Cronograma
          </button>
        </div>
      </div>

      {/* Empty State */}
      {schedules.length === 0 ? (
        <div className="text-center py-16">
          <div className="mx-auto w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900/30 dark:to-blue-900/30 rounded-full flex items-center justify-center mb-6">
            <BookOpen className="h-12 w-12 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Crie seu primeiro cronograma
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
            Organize seus estudos semanais de forma eficiente e acompanhe seu progresso
          </p>
          <button
            onClick={onCreateSchedule}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-900 transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <Plus className="h-5 w-5 mr-2" />
            Crie seu cronograma semanal agora!
          </button>
        </div>
      ) : (
        <>
          {/* Results count */}
          <div className="mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {filteredSchedules.length} cronograma{filteredSchedules.length !== 1 ? 's' : ''} encontrado{filteredSchedules.length !== 1 ? 's' : ''}
            </p>
          </div>

          {/* Schedules Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredSchedules.map((schedule) => (
              <div
                key={schedule.id}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white line-clamp-2">
                      {schedule.title}
                    </h3>
                  </div>
                  
                  {schedule.description && (
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                      {schedule.description}
                    </p>
                  )}

                  <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {schedule.weeks.length} semana{schedule.weeks.length !== 1 ? 's' : ''}
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {formatDateShort(new Date(schedule.createdAt))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => onViewSchedule(schedule)}
                      className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-900/30 rounded-md hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors duration-200"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Visualizar
                    </button>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onEditSchedule(schedule)}
                        className="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-md transition-colors duration-200"
                        title="Editar cronograma"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      {onDuplicateSchedule && (
                        <button
                          onClick={() => onDuplicateSchedule(schedule)}
                          className="p-2 text-gray-600 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/30 rounded-md transition-colors duration-200"
                          title="Duplicar cronograma"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      )}
                      <button
                        onClick={() => handleDelete(schedule.id)}
                        className={`p-2 rounded-md transition-colors duration-200 ${
                          deleteConfirm === schedule.id
                            ? 'text-white bg-red-600 hover:bg-red-700'
                            : 'text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30'
                        }`}
                        title={deleteConfirm === schedule.id ? 'Clique novamente para confirmar' : 'Excluir cronograma'}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default Dashboard;
