import React, { useState } from 'react';
import { Plus, Search, Calendar, Clock, BookOpen, Edit, Trash2, Eye, Copy, Download } from 'lucide-react';
import type { StudySchedule } from '../types';
import { formatDateShort, sortSchedulesByDate, searchSchedules } from '../utils/helpers';
import { exportAllSchedulesAsJSON } from '../utils/export';

interface DashboardProps {
  schedules: StudySchedule[];
  onCreateSchedule: () => void;
  onEditSchedule: (schedule: StudySchedule) => void;
  onViewSchedule: (schedule: StudySchedule) => void;
  onDeleteSchedule: (scheduleId: string) => void;
  onDuplicateSchedule?: (schedule: StudySchedule) => void;
}

const Dashboard: React.FC<DashboardProps> = ({
  schedules,
  onCreateSchedule,
  onEditSchedule,
  onViewSchedule,
  onDeleteSchedule,
  onDuplicateSchedule
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

  const filteredSchedules = searchSchedules(sortSchedulesByDate(schedules), searchQuery);

  const handleDelete = (scheduleId: string) => {
    if (deleteConfirm === scheduleId) {
      onDeleteSchedule(scheduleId);
      setDeleteConfirm(null);
    } else {
      setDeleteConfirm(scheduleId);
      // Auto-cancel confirmation after 3 seconds
      setTimeout(() => setDeleteConfirm(null), 3000);
    }
  };

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="bg-gradient-to-r from-purple-600 via-purple-700 to-blue-600 rounded-2xl p-8 text-white shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold mb-3 bg-gradient-to-r from-white to-purple-100 bg-clip-text text-transparent">
                Seus Cronogramas de Estudo
              </h1>
              <p className="text-purple-100 text-lg">
                Organize e gerencie seus cronogramas semanais de forma eficiente
              </p>
              <div className="flex items-center mt-4 space-x-6">
                <div className="flex items-center">
                  <BookOpen className="h-5 w-5 mr-2 text-purple-200" />
                  <span className="text-purple-100">{schedules.length} cronograma{schedules.length !== 1 ? 's' : ''}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2 text-purple-200" />
                  <span className="text-purple-100">
                    {schedules.reduce((total, schedule) => total + schedule.weeks.length, 0)} semanas totais
                  </span>
                </div>
              </div>
            </div>
            <div className="hidden lg:block">
              <div className="w-32 h-32 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
                <BookOpen className="h-16 w-16 text-white/80" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Create */}
      <div className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 mb-8 border border-gray-200/50 dark:border-gray-700/50">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar cronogramas..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border border-gray-300/50 dark:border-gray-600/50 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white/80 dark:bg-gray-700/80 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 backdrop-blur-sm transition-all duration-200"
            />
          </div>
          <div className="flex items-center space-x-3">
            {schedules.length > 0 && (
              <button
                onClick={() => exportAllSchedulesAsJSON(schedules)}
                className="inline-flex items-center px-5 py-3 border border-gray-300/50 dark:border-gray-600/50 text-sm font-medium rounded-xl text-gray-700 dark:text-gray-300 bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-900 transition-all duration-200 backdrop-blur-sm shadow-sm hover:shadow-md"
              >
                <Download className="h-4 w-4 mr-2" />
                Exportar Todos
              </button>
            )}
            <button
              onClick={onCreateSchedule}
              className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-900 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <Plus className="h-4 w-4 mr-2" />
              Novo Cronograma
            </button>
          </div>
        </div>
      </div>

      {/* Empty State */}
      {schedules.length === 0 ? (
        <div className="text-center py-20">
          <div className="relative mx-auto w-32 h-32 mb-8">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full opacity-20 animate-pulse"></div>
            <div className="relative w-full h-full bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900/30 dark:to-blue-900/30 rounded-full flex items-center justify-center backdrop-blur-sm">
              <BookOpen className="h-16 w-16 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
            Crie seu primeiro cronograma
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-lg mx-auto text-lg">
            Organize seus estudos semanais de forma eficiente e acompanhe seu progresso com cronogramas personalizados
          </p>
          <button
            onClick={onCreateSchedule}
            className="inline-flex items-center px-8 py-4 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-900 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            <Plus className="h-5 w-5 mr-2" />
            Crie seu cronograma semanal agora!
          </button>
        </div>
      ) : (
        <>
          {/* Results count */}
          <div className="mb-6">
            <p className="text-sm text-gray-600 dark:text-gray-400 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg px-4 py-2 inline-block border border-gray-200/50 dark:border-gray-700/50">
              {filteredSchedules.length} cronograma{filteredSchedules.length !== 1 ? 's' : ''} encontrado{filteredSchedules.length !== 1 ? 's' : ''}
            </p>
          </div>

          {/* Schedules Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredSchedules.map((schedule, index) => (
              <div
                key={schedule.id}
                className="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl shadow-sm border border-gray-200/50 dark:border-gray-700/50 hover:shadow-xl hover:border-purple-200 dark:hover:border-purple-700/50 transition-all duration-300 transform hover:-translate-y-1"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="p-6">
                  {/* Header with gradient accent */}
                  <div className="relative mb-4">
                    <div className="absolute -top-6 -left-6 w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-300"></div>
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white line-clamp-2 relative z-10">
                      {schedule.title}
                    </h3>
                  </div>

                  {schedule.description && (
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                      {schedule.description}
                    </p>
                  )}

                  {/* Stats with better styling */}
                  <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
                    <div className="flex items-center bg-purple-50 dark:bg-purple-900/20 px-3 py-1 rounded-full">
                      <Calendar className="h-4 w-4 mr-1 text-purple-600 dark:text-purple-400" />
                      <span className="font-medium">{schedule.weeks.length} semana{schedule.weeks.length !== 1 ? 's' : ''}</span>
                    </div>
                    <div className="flex items-center bg-blue-50 dark:bg-blue-900/20 px-3 py-1 rounded-full">
                      <Clock className="h-4 w-4 mr-1 text-blue-600 dark:text-blue-400" />
                      <span className="font-medium">{formatDateShort(new Date(schedule.createdAt))}</span>
                    </div>
                  </div>

                  {/* Action buttons with improved design */}
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => onViewSchedule(schedule)}
                      className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Visualizar
                    </button>

                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => onEditSchedule(schedule)}
                        className="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200 transform hover:scale-110"
                        title="Editar cronograma"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      {onDuplicateSchedule && (
                        <button
                          onClick={() => onDuplicateSchedule(schedule)}
                          className="p-2 text-gray-600 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/30 rounded-lg transition-all duration-200 transform hover:scale-110"
                          title="Duplicar cronograma"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      )}
                      <button
                        onClick={() => handleDelete(schedule.id)}
                        className={`p-2 rounded-lg transition-all duration-200 transform hover:scale-110 ${
                          deleteConfirm === schedule.id
                            ? 'text-white bg-red-600 hover:bg-red-700 shadow-md'
                            : 'text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30'
                        }`}
                        title={deleteConfirm === schedule.id ? 'Clique novamente para confirmar' : 'Excluir cronograma'}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default Dashboard;
