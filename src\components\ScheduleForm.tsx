import React, { useState, useEffect } from 'react';
import { <PERSON>Left, Save, Plus, Trash2, BookOpen, PenTool, Target, RotateCcw, FileText } from 'lucide-react';
import type { StudySchedule, WeekSchedule, WeekFormData } from '../types';
import { DEFAULT_SUBJECTS, EXAM_LEVELS, POPULAR_EXAMS } from '../utils/constants';
import { 
  generateId, 
  formDataToWeekSchedule, 
  weekScheduleToFormData, 
  createEmptyFormData, 
  validateFormData,
  sortWeeksByNumber
} from '../utils/helpers';

interface ScheduleFormProps {
  schedule: StudySchedule | null;
  onSave: (schedule: StudySchedule) => void;
  onCancel: () => void;
  isEditing: boolean;
}

const ScheduleForm: React.FC<ScheduleFormProps> = ({
  schedule,
  onSave,
  onCancel,
  isEditing
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [weeks, setWeeks] = useState<WeekSchedule[]>([]);
  const [currentWeekForm, setCurrentWeekForm] = useState<WeekFormData>(createEmptyFormData(1));
  const [editingWeekId, setEditingWeekId] = useState<string | null>(null);
  const [errors, setErrors] = useState<string[]>([]);
  const [showWeekForm, setShowWeekForm] = useState(false);

  useEffect(() => {
    if (schedule && isEditing) {
      setTitle(schedule.title);
      setDescription(schedule.description || '');
      setWeeks(schedule.weeks);
    }
  }, [schedule, isEditing]);

  const handleAddWeek = () => {
    const nextWeekNumber = weeks.length > 0 ? Math.max(...weeks.map(w => w.weekNumber)) + 1 : 1;
    setCurrentWeekForm(createEmptyFormData(nextWeekNumber));
    setEditingWeekId(null);
    setShowWeekForm(true);
    setErrors([]);
  };

  const handleEditWeek = (week: WeekSchedule) => {
    setCurrentWeekForm(weekScheduleToFormData(week));
    setEditingWeekId(week.id);
    setShowWeekForm(true);
    setErrors([]);
  };

  const handleSaveWeek = () => {
    const validationErrors = validateFormData(currentWeekForm);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    const weekSchedule = formDataToWeekSchedule(currentWeekForm);

    if (editingWeekId) {
      setWeeks(prev => prev.map(w => w.id === editingWeekId ? { ...weekSchedule, id: editingWeekId } : w));
    } else {
      setWeeks(prev => [...prev, weekSchedule]);
    }

    setShowWeekForm(false);
    setCurrentWeekForm(createEmptyFormData(1));
    setEditingWeekId(null);
    setErrors([]);
  };

  const handleDeleteWeek = (weekId: string) => {
    setWeeks(prev => prev.filter(w => w.id !== weekId));
  };

  const handleSaveSchedule = () => {
    if (!title.trim()) {
      setErrors(['Título do cronograma é obrigatório']);
      return;
    }

    if (weeks.length === 0) {
      setErrors(['Adicione pelo menos uma semana ao cronograma']);
      return;
    }

    const now = new Date();
    const scheduleData: StudySchedule = {
      id: schedule?.id || generateId(),
      title: title.trim(),
      description: description.trim() || undefined,
      weeks: sortWeeksByNumber(weeks),
      createdAt: schedule?.createdAt || now,
      updatedAt: now
    };

    onSave(scheduleData);
  };

  const sortedWeeks = sortWeeksByNumber(weeks);

  return (
    <div className="max-w-5xl mx-auto animate-fade-in">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 via-purple-700 to-blue-600 rounded-2xl p-8 mb-8 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <button
              onClick={onCancel}
              className="p-3 text-purple-100 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200 backdrop-blur-sm transform hover:scale-110"
            >
              <ArrowLeft className="h-6 w-6" />
            </button>
            <div>
              <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-white to-purple-100 bg-clip-text text-transparent">
                {isEditing ? 'Editar Cronograma' : 'Novo Cronograma'}
              </h1>
              <p className="text-purple-100">
                {isEditing ? 'Modifique as informações do seu cronograma' : 'Crie um cronograma personalizado para seus estudos'}
              </p>
            </div>
          </div>
          <button
            onClick={handleSaveSchedule}
            className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-purple-700 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white focus:ring-offset-purple-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            <Save className="h-4 w-4 mr-2" />
            Salvar Cronograma
          </button>
        </div>
      </div>

      {/* Errors */}
      {errors.length > 0 && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-md">
          <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
            {errors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Schedule Info */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Informações do Cronograma
        </h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Título *
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Ex: Cronograma ENEM 2024"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Descrição (opcional)
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Descreva o objetivo deste cronograma..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            />
          </div>
        </div>
      </div>

      {/* Weeks Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Semanas de Estudo ({weeks.length})
          </h2>
          <button
            onClick={handleAddWeek}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800 transition-all duration-200"
          >
            <Plus className="h-4 w-4 mr-2" />
            Adicionar Semana
          </button>
        </div>

        {/* Weeks List */}
        {sortedWeeks.length > 0 && (
          <div className="space-y-4 mb-6">
            {sortedWeeks.map((week) => (
              <div
                key={week.id}
                className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:border-purple-300 dark:hover:border-purple-500 transition-colors duration-200"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                      Semana {week.weekNumber} - {week.subject}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                      <div>
                        <span className="font-medium">Tópicos:</span> {week.topics.slice(0, 2).join(', ')}
                        {week.topics.length > 2 && ` (+${week.topics.length - 2} mais)`}
                      </div>
                      <div>
                        <span className="font-medium">Redação:</span> {week.essayTheme}
                      </div>
                      <div>
                        <span className="font-medium">Meta:</span> {week.questionsGoal.quantity} questões de {week.questionsGoal.subject}
                      </div>
                      <div>
                        <span className="font-medium">Simulado:</span> {week.suggestedExam.name}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => handleEditWeek(week)}
                      className="p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-md transition-colors duration-200"
                      title="Editar semana"
                    >
                      <PenTool className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteWeek(week.id)}
                      className="p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-md transition-colors duration-200"
                      title="Excluir semana"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Week Form */}
        {showWeekForm && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {editingWeekId ? 'Editar Semana' : 'Nova Semana'}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Week Number and Subject */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <BookOpen className="inline h-4 w-4 mr-1" />
                    Número da Semana *
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={currentWeekForm.weekNumber}
                    onChange={(e) => setCurrentWeekForm(prev => ({ ...prev, weekNumber: parseInt(e.target.value) || 1 }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <BookOpen className="inline h-4 w-4 mr-1" />
                    Matéria da Semana *
                  </label>
                  <select
                    value={currentWeekForm.subject}
                    onChange={(e) => setCurrentWeekForm(prev => ({ ...prev, subject: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">Selecione uma matéria</option>
                    {DEFAULT_SUBJECTS.map(subject => (
                      <option key={subject} value={subject}>{subject}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <PenTool className="inline h-4 w-4 mr-1" />
                    Tema da Redação *
                  </label>
                  <input
                    type="text"
                    value={currentWeekForm.essayTheme}
                    onChange={(e) => setCurrentWeekForm(prev => ({ ...prev, essayTheme: e.target.value }))}
                    placeholder="Ex: Desafios da educação no Brasil"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  />
                </div>
              </div>

              {/* Topics */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <FileText className="inline h-4 w-4 mr-1" />
                  Tópicos de Estudo * (um por linha)
                </label>
                <textarea
                  value={currentWeekForm.topics}
                  onChange={(e) => setCurrentWeekForm(prev => ({ ...prev, topics: e.target.value }))}
                  placeholder="Funções quadráticas&#10;Inequações&#10;Sistemas lineares"
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                />
              </div>
            </div>

            {/* Goals and Review */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 dark:text-white">
                  <Target className="inline h-4 w-4 mr-1" />
                  Meta de Questões
                </h4>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Matéria *
                  </label>
                  <select
                    value={currentWeekForm.questionsGoal.subject}
                    onChange={(e) => setCurrentWeekForm(prev => ({
                      ...prev,
                      questionsGoal: { ...prev.questionsGoal, subject: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">Selecione uma matéria</option>
                    {DEFAULT_SUBJECTS.map(subject => (
                      <option key={subject} value={subject}>{subject}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Quantidade *
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={currentWeekForm.questionsGoal.quantity}
                    onChange={(e) => setCurrentWeekForm(prev => ({
                      ...prev,
                      questionsGoal: { ...prev.questionsGoal, quantity: e.target.value }
                    }))}
                    placeholder="45"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 dark:text-white">
                  <RotateCcw className="inline h-4 w-4 mr-1" />
                  Revisão da Semana
                </h4>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Descrição *
                  </label>
                  <input
                    type="text"
                    value={currentWeekForm.weeklyReview.description}
                    onChange={(e) => setCurrentWeekForm(prev => ({
                      ...prev,
                      weeklyReview: { ...prev.weeklyReview, description: e.target.value }
                    }))}
                    placeholder="Ex: Questões dos conteúdos anteriores"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Quantidade *
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={currentWeekForm.weeklyReview.quantity}
                    onChange={(e) => setCurrentWeekForm(prev => ({
                      ...prev,
                      weeklyReview: { ...prev.weeklyReview, quantity: e.target.value }
                    }))}
                    placeholder="10"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  />
                </div>
              </div>
            </div>

            {/* Suggested Exam */}
            <div className="mt-6">
              <h4 className="font-medium text-gray-900 dark:text-white mb-4">
                <FileText className="inline h-4 w-4 mr-1" />
                Simulado Sugerido
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nome do Simulado *
                  </label>
                  <select
                    value={currentWeekForm.suggestedExam.name}
                    onChange={(e) => setCurrentWeekForm(prev => ({
                      ...prev,
                      suggestedExam: { ...prev.suggestedExam, name: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">Selecione um simulado</option>
                    {POPULAR_EXAMS.map(exam => (
                      <option key={exam} value={exam}>{exam}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nível *
                  </label>
                  <select
                    value={currentWeekForm.suggestedExam.level}
                    onChange={(e) => setCurrentWeekForm(prev => ({
                      ...prev,
                      suggestedExam: { ...prev.suggestedExam, level: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    {EXAM_LEVELS.map(level => (
                      <option key={level} value={level}>{level}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Número de Questões *
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={currentWeekForm.suggestedExam.questionsCount}
                    onChange={(e) => setCurrentWeekForm(prev => ({
                      ...prev,
                      suggestedExam: { ...prev.suggestedExam, questionsCount: e.target.value }
                    }))}
                    placeholder="45"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  />
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={() => {
                  setShowWeekForm(false);
                  setCurrentWeekForm(createEmptyFormData(1));
                  setEditingWeekId(null);
                  setErrors([]);
                }}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              >
                Cancelar
              </button>
              <button
                onClick={handleSaveWeek}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-800 transition-all duration-200"
              >
                <Save className="h-4 w-4 mr-2" />
                {editingWeekId ? 'Atualizar Semana' : 'Adicionar Semana'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ScheduleForm;
