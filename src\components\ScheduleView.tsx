import React, { useState, useEffect, useRef } from 'react';
import { ArrowLeft, Edit, Calendar, Clock, BookOpen, PenTool, Target, RotateCcw, FileText, ChevronDown, ChevronUp, Download, Printer, MoreHorizontal } from 'lucide-react';
import type { StudySchedule, WeekSchedule } from '../types';
import { formatDate, sortWeeksByNumber } from '../utils/helpers';
import { exportAsText, exportAsMarkdown, exportAsJSON, printSchedule, generateStudySummary } from '../utils/export';

interface ScheduleViewProps {
  schedule: StudySchedule;
  onEdit: () => void;
  onBack: () => void;
}

interface WeekCardProps {
  week: WeekSchedule;
  isExpanded: boolean;
  onToggle: () => void;
}

const WeekCard: React.FC<WeekCardProps> = ({ week, isExpanded, onToggle }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Week Header */}
      <div 
        className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200"
        onClick={onToggle}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg text-white font-semibold">
              {week.weekNumber}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Semana {week.weekNumber}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {week.subject}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {isExpanded ? 'Recolher' : 'Expandir'}
            </span>
            {isExpanded ? (
              <ChevronUp className="h-5 w-5 text-gray-400" />
            ) : (
              <ChevronDown className="h-5 w-5 text-gray-400" />
            )}
          </div>
        </div>
      </div>

      {/* Week Content */}
      {isExpanded && (
        <div className="border-t border-gray-200 dark:border-gray-700 p-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Topics */}
              <div>
                <h4 className="flex items-center text-sm font-medium text-gray-900 dark:text-white mb-3">
                  <BookOpen className="h-4 w-4 mr-2 text-purple-600 dark:text-purple-400" />
                  Tópicos de Estudo
                </h4>
                <ul className="space-y-2">
                  {week.topics.map((topic, index) => (
                    <li key={index} className="flex items-start">
                      <span className="inline-block w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span className="text-sm text-gray-700 dark:text-gray-300">{topic}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Essay Theme */}
              <div>
                <h4 className="flex items-center text-sm font-medium text-gray-900 dark:text-white mb-3">
                  <PenTool className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                  Tema da Redação
                </h4>
                <p className="text-sm text-gray-700 dark:text-gray-300 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md">
                  {week.essayTheme}
                </p>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Questions Goal */}
              <div>
                <h4 className="flex items-center text-sm font-medium text-gray-900 dark:text-white mb-3">
                  <Target className="h-4 w-4 mr-2 text-green-600 dark:text-green-400" />
                  Meta de Questões
                </h4>
                <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-md">
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    <span className="font-medium">{week.questionsGoal.quantity} questões</span> de {week.questionsGoal.subject}
                  </p>
                </div>
              </div>

              {/* Weekly Review */}
              <div>
                <h4 className="flex items-center text-sm font-medium text-gray-900 dark:text-white mb-3">
                  <RotateCcw className="h-4 w-4 mr-2 text-orange-600 dark:text-orange-400" />
                  Revisão da Semana
                </h4>
                <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-md">
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    <span className="font-medium">{week.weeklyReview.quantity} questões</span>
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    {week.weeklyReview.description}
                  </p>
                </div>
              </div>

              {/* Suggested Exam */}
              <div>
                <h4 className="flex items-center text-sm font-medium text-gray-900 dark:text-white mb-3">
                  <FileText className="h-4 w-4 mr-2 text-red-600 dark:text-red-400" />
                  Simulado Sugerido
                </h4>
                <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-md">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {week.suggestedExam.name}
                  </p>
                  <div className="flex items-center justify-between mt-2 text-xs text-gray-600 dark:text-gray-400">
                    <span>Nível: {week.suggestedExam.level}</span>
                    <span>{week.suggestedExam.questionsCount} questões</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const ScheduleView: React.FC<ScheduleViewProps> = ({ schedule, onEdit, onBack }) => {
  const [expandedWeeks, setExpandedWeeks] = useState<Set<string>>(new Set());
  const [showExportMenu, setShowExportMenu] = useState(false);
  const exportMenuRef = useRef<HTMLDivElement>(null);

  // Close export menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (exportMenuRef.current && !exportMenuRef.current.contains(event.target as Node)) {
        setShowExportMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleWeek = (weekId: string) => {
    setExpandedWeeks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(weekId)) {
        newSet.delete(weekId);
      } else {
        newSet.add(weekId);
      }
      return newSet;
    });
  };

  const expandAll = () => {
    setExpandedWeeks(new Set(schedule.weeks.map(w => w.id)));
  };

  const collapseAll = () => {
    setExpandedWeeks(new Set());
  };

  const sortedWeeks = sortWeeksByNumber(schedule.weeks);

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors duration-200"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {schedule.title}
            </h1>
            {schedule.description && (
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                {schedule.description}
              </p>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-3">
          {/* Export Menu */}
          <div className="relative" ref={exportMenuRef}>
            <button
              onClick={() => setShowExportMenu(!showExportMenu)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-900 transition-all duration-200"
            >
              <Download className="h-4 w-4 mr-2" />
              Exportar
              <MoreHorizontal className="h-4 w-4 ml-2" />
            </button>

            {showExportMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 z-10">
                <div className="py-1">
                  <button
                    onClick={() => {
                      exportAsText(schedule);
                      setShowExportMenu(false);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    📄 Exportar como Texto
                  </button>
                  <button
                    onClick={() => {
                      exportAsMarkdown(schedule);
                      setShowExportMenu(false);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    📝 Exportar como Markdown
                  </button>
                  <button
                    onClick={() => {
                      exportAsJSON(schedule);
                      setShowExportMenu(false);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    💾 Exportar como JSON
                  </button>
                  <hr className="my-1 border-gray-200 dark:border-gray-600" />
                  <button
                    onClick={() => {
                      printSchedule(schedule);
                      setShowExportMenu(false);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    🖨️ Imprimir
                  </button>
                  <button
                    onClick={() => {
                      const summary = generateStudySummary(schedule);
                      alert(summary);
                      setShowExportMenu(false);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    📊 Ver Resumo
                  </button>
                </div>
              </div>
            )}
          </div>

          <button
            onClick={onEdit}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-900 transition-all duration-200"
          >
            <Edit className="h-4 w-4 mr-2" />
            Editar Cronograma
          </button>
        </div>
      </div>

      {/* Schedule Info */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              {schedule.weeks.length} semana{schedule.weeks.length !== 1 ? 's' : ''}
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              Criado em {formatDate(new Date(schedule.createdAt))}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={expandAll}
              className="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 font-medium"
            >
              Expandir Todas
            </button>
            <span className="text-gray-300 dark:text-gray-600">|</span>
            <button
              onClick={collapseAll}
              className="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 font-medium"
            >
              Recolher Todas
            </button>
          </div>
        </div>
      </div>

      {/* Weeks */}
      <div className="space-y-4">
        {sortedWeeks.map((week) => (
          <WeekCard
            key={week.id}
            week={week}
            isExpanded={expandedWeeks.has(week.id)}
            onToggle={() => toggleWeek(week.id)}
          />
        ))}
      </div>
    </div>
  );
};

export default ScheduleView;
