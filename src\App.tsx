import { useState, useEffect } from 'react';
import type { StudySchedule, AppSettings } from './types';
import { STORAGE_KEYS, APP_CONFIG } from './utils/constants';
import { generateId } from './utils/helpers';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import ScheduleForm from './components/ScheduleForm';
import ScheduleView from './components/ScheduleView';
import { useLocalStorage } from './hooks/useLocalStorage';

type ViewMode = 'dashboard' | 'create' | 'view' | 'edit';

function App() {
  const [schedules, setSchedules] = useLocalStorage<StudySchedule[]>(STORAGE_KEYS.SCHEDULES, []);
  const [settings, setSettings] = useLocalStorage<AppSettings>(STORAGE_KEYS.SETTINGS, {
    darkMode: true,
    language: 'pt-BR',
    defaultSubjects: []
  });
  
  const [currentView, setCurrentView] = useState<ViewMode>('dashboard');
  const [selectedSchedule, setSelectedSchedule] = useState<StudySchedule | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Apply dark mode (always enabled)
  useEffect(() => {
    document.documentElement.classList.add('dark');
    // Force dark mode in settings if not already set
    if (!settings.darkMode) {
      setSettings(prev => ({ ...prev, darkMode: true }));
    }
  }, []);

  const handleCreateSchedule = () => {
    setSelectedSchedule(null);
    setCurrentView('create');
    setSidebarOpen(false);
  };

  const handleEditSchedule = (schedule: StudySchedule) => {
    setSelectedSchedule(schedule);
    setCurrentView('edit');
    setSidebarOpen(false);
  };

  const handleViewSchedule = (schedule: StudySchedule) => {
    setSelectedSchedule(schedule);
    setCurrentView('view');
    setSidebarOpen(false);
  };

  const handleDeleteSchedule = (scheduleId: string) => {
    setSchedules(prev => prev.filter(s => s.id !== scheduleId));
    if (selectedSchedule?.id === scheduleId) {
      setCurrentView('dashboard');
      setSelectedSchedule(null);
    }
  };

  const handleDuplicateSchedule = (schedule: StudySchedule) => {
    const now = new Date();
    const duplicatedSchedule: StudySchedule = {
      ...schedule,
      id: generateId(),
      title: `${schedule.title} (Cópia)`,
      weeks: schedule.weeks.map(week => ({
        ...week,
        id: generateId(),
        createdAt: now,
        updatedAt: now
      })),
      createdAt: now,
      updatedAt: now
    };
    setSchedules(prev => [...prev, duplicatedSchedule]);
  };

  const handleSaveSchedule = (schedule: StudySchedule) => {
    if (currentView === 'edit' && selectedSchedule) {
      setSchedules(prev => prev.map(s => s.id === schedule.id ? schedule : s));
    } else {
      setSchedules(prev => [...prev, schedule]);
    }
    setCurrentView('dashboard');
    setSelectedSchedule(null);
  };



  const renderContent = () => {
    switch (currentView) {
      case 'create':
      case 'edit':
        return (
          <ScheduleForm
            schedule={selectedSchedule}
            onSave={handleSaveSchedule}
            onCancel={() => setCurrentView('dashboard')}
            isEditing={currentView === 'edit'}
          />
        );
      case 'view':
        return selectedSchedule ? (
          <ScheduleView
            schedule={selectedSchedule}
            onEdit={() => handleEditSchedule(selectedSchedule)}
            onBack={() => setCurrentView('dashboard')}
          />
        ) : null;
      default:
        return (
          <Dashboard
            schedules={schedules}
            onCreateSchedule={handleCreateSchedule}
            onEditSchedule={handleEditSchedule}
            onViewSchedule={handleViewSchedule}
            onDeleteSchedule={handleDeleteSchedule}
            onDuplicateSchedule={handleDuplicateSchedule}
          />
        );
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200`}>
      <Header
        onMenuClick={() => setSidebarOpen(!sidebarOpen)}
        onCreateSchedule={handleCreateSchedule}
      />
      
      <div className="flex">
        <Sidebar
          isOpen={sidebarOpen}
          schedules={schedules}
          onSelectSchedule={handleViewSchedule}
          onCreateSchedule={handleCreateSchedule}
          onClose={() => setSidebarOpen(false)}
        />
        
        <main className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-64' : ''}`}>
          <div className="p-4 lg:p-6">
            {renderContent()}
          </div>
        </main>
      </div>
      
      {/* Footer */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 px-6">
        <div className="text-center text-sm text-gray-600 dark:text-gray-400">
          Criado por{' '}
          <a
            href={APP_CONFIG.authorUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 font-medium"
          >
            {APP_CONFIG.author}
          </a>
        </div>
      </footer>
    </div>
  );
}

export default App;
